-- stats_panel.lua
-- Handles the stats panel content and functionality

local StatsPanel = {}

local skillCards = {}

-- Skill configuration table
local skillConfigs = {
  ninjutsu = {
    name = "Ninjutsu",
    color = "#4a90e2", -- Blue
    getValue = function(player) return tonumber(player:getNinjutsu()) or 0 end,
    getPercent = function(player) return tonumber(player:getNinjutsuPercent()) or 0 end,
    getBase = function(player) return tonumber(player:getBaseNinjutsu()) or 0 end
  },
  taijutsu = {
    name = "Taijutsu", 
    color = "#e74c3c", -- Red
    getValue = function(player) 
      return tonumber(player:getSkillLevel(0)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(0)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(0)) or 0 
    end
  },
  kenjutsu = {
    name = "Kenju<PERSON>",
    color = "#f39c12", -- Orange
    getValue = function(player) 
      return tonumber(player:getSkillLevel(1)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(1)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(1)) or 0 
    end
  },
  shurikenjutsu = {
    name = "Shurikenjutsu",
    color = "#9b59b6", -- Purple
    getValue = function(player) 
      return tonumber(player:getSkillLevel(2)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(2)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(2)) or 0 
    end
  }
}

function StatsPanel.show(contentPanel)
  if not contentPanel then
    g_logger.error("StatsPanel: contentPanel is nil")
    return
  end
  
  -- Add padding to the content panel
  contentPanel:setPaddingLeft(6)
  contentPanel:setPaddingRight(6)
  contentPanel:setPaddingTop(6)
  contentPanel:setPaddingBottom(6)
  
  -- Create grid layout
  local gridLayout = UIGridLayout.create(contentPanel)
  gridLayout:setCellSize({width = 64, height = 44})
  gridLayout:setCellSpacing(3)
  gridLayout:setFlow(true)
  contentPanel:setLayout(gridLayout)
  
  -- Create skill cards
  skillCards = {}
  for skillType, config in pairs(skillConfigs) do
    local card = StatsPanel.createSkillCard(skillType, contentPanel, config)
    if card then
      skillCards[skillType] = card
    end
  end
end

function StatsPanel.createSkillCard(skillType, contentPanel, config)
  local card = g_ui.createWidget('SkillStatCard', contentPanel)
  if not card then
    g_logger.error("StatsPanel: Failed to create SkillStatCard widget for " .. skillType)
    return nil
  end
  
  -- Set icon color
  local iconPlaceholder = card:getChildById('iconPlaceholder')
  if iconPlaceholder then
    iconPlaceholder:setBackgroundColor(config.color)
  end
  
  -- Update card with current data
  StatsPanel.updateSkillCardData(skillType, card, config)
  return card
end

function StatsPanel.updateSkillCardData(skillType, card, config)
  if not card then 
    return 
  end
  
  config = config or skillConfigs[skillType]
  if not config then
    g_logger.error("StatsPanel: Unknown skill type: " .. skillType)
    return
  end
  
  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.error("StatsPanel: No local player found when updating " .. skillType .. " card")
    return
  end
  
  -- Get skill values
  local skillValue = config.getValue(localPlayer)
  local skillPercent = config.getPercent(localPlayer)
  local baseSkill = config.getBase(localPlayer)
  
  -- Update stat text
  local statText = card:getChildById('statText')
  if statText then
    statText:setText(math.floor(skillValue) .. "/" .. math.floor(baseSkill))
  end
  
  -- Update progress bar
  local progressBar = card:getChildById('progressBar')
  if progressBar then
    progressBar:setPercent(skillPercent)
  end
end

function StatsPanel.onPlayerSkillChange(skillType, newValue, newPercent, oldValue, oldPercent)
  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("StatsPanel: Unknown skill type: " .. tostring(skillType))
    return
  end
  
  local card = skillCards[skillType]
  if card then
    StatsPanel.updateSkillCardData(skillType, card, config)
  end
end

function StatsPanel.getSkillConfigs()
  return skillConfigs
end

return StatsPanel 