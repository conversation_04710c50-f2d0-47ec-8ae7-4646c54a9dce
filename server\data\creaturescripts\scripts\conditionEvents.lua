local CUSTOM_OPCODE_CONDITION = 69

-- Helper function to create the condition payload
local function createConditionPayload(action, condition, ticks)
    local payload = {
        action = action,
        type = condition:getType(),
        id = condition:getId(),
        subId = condition:getSubId()
    }
    if ticks then
        payload.ticks = ticks
    end
    return payload
end

function onAddCondition(creature, condition)
    print("==== onAddCondition called ====")
    if creature:isPlayer() then

        local payload = createConditionPayload("add", condition, condition:getTicks())

        creature:sendExtendedOpcode(CUSTOM_OPCODE_CONDITION, json.encode(payload))
        print("Sent add condition opcode for type:", condition:getType(), "id:", condition:getId())
    end
    return true
end

function onRemoveCondition(creature, condition)
    print("==== onRemoveCondition called ====")
    if creature:isPlayer() then
        -- Notify client about the removed condition
        local payload = createConditionPayload("remove", condition)

        creature:sendExtendedOpcode(CUSTOM_OPCODE_CONDITION, json.encode(payload))
        print("Sent remove condition opcode for type:", condition:getType(), "id:", condition:getId())
    end
    return true
end