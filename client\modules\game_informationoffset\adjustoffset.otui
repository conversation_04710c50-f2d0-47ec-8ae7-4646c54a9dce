MainWindow
  id: adjustOffsetWindow
  title: Adjust information window offsets for creatures
  size: 400 600
  draggable: true
  visible: false
  clipping: true
  border-width: 1
  padding: 15
  
  Label
    id: infoLabel
    text: Adjust information window offsets for creatures
    anchors.top: parent.top
    anchors.left: parent.left
    anchors.right: parent.right
    text-auto-resize: true
    text-align: center
    margin-top: 20
  
  Label
    id: directionLabel
    text: Direction-specific idle offsets:
    anchors.top: infoLabel.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    text-auto-resize: true
    margin-top: 20
    
  Panel
    id: directionPanel
    anchors.top: directionLabel.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    margin-top: 10
    height: 160
    
    Label
      id: northLabel
      text: North:
      anchors.top: parent.top
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 5
      
    SpinBox
      id: northXSpinBox
      width: 60
      anchors.top: parent.top
      anchors.left: northLabel.right
      margin-left: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("north")
      
    SpinBox
      id: northYSpinBox
      width: 60
      anchors.top: parent.top
      anchors.left: northXSpinBox.right
      margin-left: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("north")
      
    Label
      id: eastLabel
      text: East:
      anchors.top: northLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: eastXSpinBox
      width: 60
      anchors.top: northXSpinBox.bottom
      anchors.left: eastLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("east")
      
    SpinBox
      id: eastYSpinBox
      width: 60
      anchors.top: northYSpinBox.bottom
      anchors.left: eastXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("east")
      
    Label
      id: southLabel
      text: South:
      anchors.top: eastLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: southXSpinBox
      width: 60
      anchors.top: eastXSpinBox.bottom
      anchors.left: southLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("south")
      
    SpinBox
      id: southYSpinBox
      width: 60
      anchors.top: eastYSpinBox.bottom
      anchors.left: southXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("south")
      
    Label
      id: westLabel
      text: West:
      anchors.top: southLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: westXSpinBox
      width: 60
      anchors.top: southXSpinBox.bottom
      anchors.left: westLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("west")
      
    SpinBox
      id: westYSpinBox
      width: 60
      anchors.top: southYSpinBox.bottom
      anchors.left: westXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyDirectionOffset("west")
  
  Label
    id: walkingDirectionLabel
    text: Direction-specific walking offsets:
    anchors.top: directionPanel.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    text-auto-resize: true
    margin-top: 20
    
  Panel
    id: walkingDirectionPanel
    anchors.top: walkingDirectionLabel.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    margin-top: 10
    height: 160
    
    Label
      id: walkingNorthLabel
      text: North:
      anchors.top: parent.top
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 5
      
    SpinBox
      id: walkingNorthXSpinBox
      width: 60
      anchors.top: parent.top
      anchors.left: walkingNorthLabel.right
      margin-left: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("north")
      
    SpinBox
      id: walkingNorthYSpinBox
      width: 60
      anchors.top: parent.top
      anchors.left: walkingNorthXSpinBox.right
      margin-left: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("north")
      
    Label
      id: walkingEastLabel
      text: East:
      anchors.top: walkingNorthLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: walkingEastXSpinBox
      width: 60
      anchors.top: walkingNorthXSpinBox.bottom
      anchors.left: walkingEastLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("east")
      
    SpinBox
      id: walkingEastYSpinBox
      width: 60
      anchors.top: walkingNorthYSpinBox.bottom
      anchors.left: walkingEastXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("east")
      
    Label
      id: walkingSouthLabel
      text: South:
      anchors.top: walkingEastLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: walkingSouthXSpinBox
      width: 60
      anchors.top: walkingEastXSpinBox.bottom
      anchors.left: walkingSouthLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("south")
      
    SpinBox
      id: walkingSouthYSpinBox
      width: 60
      anchors.top: walkingEastYSpinBox.bottom
      anchors.left: walkingSouthXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("south")
      
    Label
      id: walkingWestLabel
      text: West:
      anchors.top: walkingSouthLabel.bottom
      anchors.left: parent.left
      text-auto-resize: true
      margin-top: 15
      
    SpinBox
      id: walkingWestXSpinBox
      width: 60
      anchors.top: walkingSouthXSpinBox.bottom
      anchors.left: walkingWestLabel.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("west")
      
    SpinBox
      id: walkingWestYSpinBox
      width: 60
      anchors.top: walkingSouthYSpinBox.bottom
      anchors.left: walkingWestXSpinBox.right
      margin-left: 10
      margin-top: 10
      minimum: -100
      maximum: 100
      @onValueChange: modules.game_informationoffset.applyWalkingDirectionOffset("west")
      
  Button
    id: copyButton
    text: Copy Offsets
    anchors.bottom: parent.bottom
    anchors.right: saveButton.left
    width: 120
    margin-bottom: 15
    margin-right: 10
    @onClick: modules.game_informationoffset.copyOffsets()
    
  Button
    id: saveButton
    text: Save & Apply
    anchors.bottom: parent.bottom
    anchors.right: closeButton.left
    width: 120
    margin-bottom: 15
    margin-right: 10
    @onClick: modules.game_informationoffset.saveAndApplyOffsets()
    
  Button
    id: closeButton
    text: Close & Revert
    anchors.bottom: parent.bottom
    anchors.right: parent.right
    width: 120
    margin-bottom: 15
    margin-right: 15
    @onClick: modules.game_informationoffset.hideWindow() 