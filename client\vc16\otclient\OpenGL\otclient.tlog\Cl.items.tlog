C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\animatedtext.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\animatedtext.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\animator.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\animator.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\client.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\client.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\container.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\container.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\creature.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\creature.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\creatures.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\creatures.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\effect.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\effect.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\game.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\game.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\healthbars.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\healthbars.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\houses.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\houses.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\item.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\item.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\itemtype.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\itemtype.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\lightview.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\lightview.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\localplayer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\localplayer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\luafunctions_client.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\luafunctions_client.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\luavaluecasts_client.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\luavaluecasts_client.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\map.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\map.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\mapio.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\mapio.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\mapview.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\mapview.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\minimap.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\minimap.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\missile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\missile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\outfit.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\outfit.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\player.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\player.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\pngunpacker.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\pngunpacker.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\protocolcodes.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\protocolcodes.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\protocolgame.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\protocolgame.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\protocolgameparse.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\protocolgameparse.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\protocolgamesend.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\protocolgamesend.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\spritemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\spritemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\statictext.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\statictext.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\thing.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\thing.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\thingtype.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\thingtype.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\thingtypemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\thingtypemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\tile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\tile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\towns.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\towns.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uicreature.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uicreature.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uiitem.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uiitem.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uimap.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uimap.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uimapanchorlayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uimapanchorlayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uiminimap.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uiminimap.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uiprogressrect.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uiprogressrect.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\client\uisprite.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\client\uisprite.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\adaptiverenderer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\adaptiverenderer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\application.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\application.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\asyncdispatcher.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\asyncdispatcher.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\binarytree.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\binarytree.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\clock.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\clock.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\config.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\config.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\configmanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\configmanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\event.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\event.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\eventdispatcher.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\eventdispatcher.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\filestream.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\filestream.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\graphicalapplication.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\graphicalapplication.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\logger.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\logger.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\module.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\module.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\modulemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\modulemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\resourcemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\resourcemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\scheduledevent.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\scheduledevent.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\core\timer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\core\timer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\animatedtexture.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\animatedtexture.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\apngloader.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\apngloader.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\atlas.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\atlas.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\bitmapfont.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\bitmapfont.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\cachedtext.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\cachedtext.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\coordsbuffer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\coordsbuffer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\drawcache.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\drawcache.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\drawqueue.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\drawqueue.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\fontmanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\fontmanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\framebuffer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\framebuffer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\framebuffermanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\framebuffermanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\graph.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\graph.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\graphics.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\graphics.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\hardwarebuffer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\hardwarebuffer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\image.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\image.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\painter.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\painter.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\paintershaderprogram.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\paintershaderprogram.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\shader.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\shader.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\shadermanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\shadermanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\shaderprogram.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\shaderprogram.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\textrender.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\textrender.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\texture.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\texture.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\graphics\texturemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\graphics\texturemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\http\http.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\http\http.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\http\session.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\http\session.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\http\websocket.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\http\websocket.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\input\mouse.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\input\mouse.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luaengine\lbitlib.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luaengine\lbitlib.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luaengine\luaexception.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luaengine\luaexception.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luaengine\luainterface.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luaengine\luainterface.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luaengine\luaobject.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luaengine\luaobject.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luaengine\luavaluecasts.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luaengine\luavaluecasts.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\luafunctions.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\luafunctions.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\connection.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\connection.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\inputmessage.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\inputmessage.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\outputmessage.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\outputmessage.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\packet_player.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\packet_player.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\packet_recorder.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\packet_recorder.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\protocol.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\protocol.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\net\server.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\net\server.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\otml\otmldocument.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\otml\otmldocument.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\otml\otmlemitter.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\otml\otmlemitter.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\otml\otmlexception.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\otml\otmlexception.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\otml\otmlnode.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\otml\otmlnode.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\otml\otmlparser.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\otml\otmlparser.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\platform\platform.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\platform\platform.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\platform\platformwindow.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\platform\platformwindow.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\platform\win32crashhandler.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\platform\win32crashhandler.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\platform\win32platform.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\platform\win32platform.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\platform\win32window.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\platform\win32window.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\proxy\proxy.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\proxy\proxy.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\proxy\proxy_client.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\proxy\proxy_client.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\combinedsoundsource.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\combinedsoundsource.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\oggsoundfile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\oggsoundfile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\soundbuffer.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\soundbuffer.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\soundchannel.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\soundchannel.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\soundfile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\soundfile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\soundmanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\soundmanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\soundsource.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\soundsource.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\sound\streamsoundsource.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\sound\streamsoundsource.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\demangle.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\demangle.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\math.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\math.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\net.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\net.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\string.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\string.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\time.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\time.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\stdext\uri.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\stdext\uri.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uianchorlayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uianchorlayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiboxlayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiboxlayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uigridlayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uigridlayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uihorizontallayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uihorizontallayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uilayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uilayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uimanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uimanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uitextedit.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uitextedit.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uitranslator.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uitranslator.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiverticallayout.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiverticallayout.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiwidget.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiwidget.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiwidgetbasestyle.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiwidgetbasestyle.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiwidgetimage.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiwidgetimage.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\ui\uiwidgettext.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\ui\uiwidgettext.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\util\color.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\util\color.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\util\crypt.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\util\crypt.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\util\extras.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\util\extras.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\util\qrcodegen.c;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\util\qrcodegen.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\util\stats.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\util\stats.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\xml\tinystr.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\xml\tinystr.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\xml\tinyxml.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\xml\tinyxml.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\xml\tinyxmlerror.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\xml\tinyxmlerror.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\framework\xml\tinyxmlparser.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\framework\xml\tinyxmlparser.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\client\src\main.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\client\vc16\otclient\OpenGL\src\main.obj
