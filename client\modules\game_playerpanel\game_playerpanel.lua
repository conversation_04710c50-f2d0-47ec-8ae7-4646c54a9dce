-- game_playerpanel.lua
-- Manages the player panel UI with dynamic button loading

-- Import panel modules
local StatsPanel = require('panels/stats_panel')
local OptionsPanel = require('panels/options_panel')
local GearPanel = require('panels/gear_panel')

local playerPanel = nil
local contentPanel = nil
local topButtonsPanel = nil
local bottomButtonsPanel = nil
local currentContent = "none"
local buttons = {}
local eventConnections = {}

-- Button configuration - defines which buttons are available and where they go
local buttonConfigs = {
  -- Top buttons
  stats = {
    text = "Stats",
    position = "top",
    order = 1,
    enabled = true,
    onClick = function() showContent('stats') end
  },
  gear = {
    text = "Gear",
    position = "top",
    order = 2,
    enabled = true,
    onClick = function() showContent('gear') end
  },
  inventory = {
    text = "Inventory",
    position = "top",
    order = 3,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('inventory') end
  },
  pvp = {
    text = "PvP",
    position = "top",
    order = 4,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('pvp') end
  },
  test = {
    text = "Test",
    position = "top",
    order = 5,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('test') end
  },

  -- Bottom buttons
  quests = {
    text = "Quests",
    position = "bottom",
    order = 1,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('quests') end
  },
  spells = {
    text = "Spells",
    position = "bottom",
    order = 2,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('spells') end
  },
  options = {
    text = "Options",
    position = "bottom",
    order = 3,
    enabled = true,
    onClick = function() showContent('options') end
  },
  help = {
    text = "Help",
    position = "bottom",
    order = 4,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('help') end
  },
  logout = {
    text = "Logout",
    position = "bottom",
    order = 5,
    enabled = false, -- Not implemented yet
    onClick = function() showContent('logout') end
  }
}

function init()
  connect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    online()
  end
end

function terminate()
  disconnect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    offline()
  end
end

function online()
  if playerPanel then
    playerPanel:destroy()
  end

  local parent = modules.game_interface.getRootPanel()
  if not parent then
    g_logger.error("GamePlayerPanel: Could not find root game panel to attach to.")
    return
  end

  local loadedUI = g_ui.loadUI('game_playerpanel', parent)
  if not loadedUI then
    g_logger.error("GamePlayerPanel: g_ui.loadUI('game_playerpanel') failed to load.")
    return
  end

  playerPanel = loadedUI
  contentPanel = playerPanel:getChildById('contentPanel')
  topButtonsPanel = playerPanel:getChildById('topButtonsPanel')
  bottomButtonsPanel = playerPanel:getChildById('bottomButtonsPanel')

  if not contentPanel or not topButtonsPanel or not bottomButtonsPanel then
    g_logger.error("GamePlayerPanel: Required panels not found")
    return
  end

  createButtons()
  connectPlayerEvents()

  playerPanel:setVisible(true)

  -- Apply saved opacity from settings
  local savedOpacity = g_settings.getNumber('playerPanelOpacity', 100)
  if savedOpacity ~= 100 then
    local opacity = savedOpacity / 100.0
    playerPanel:setOpacity(opacity)
  end
end

function offline()
  disconnectPlayerEvents()

  -- Clean up gear panel if it's currently active
  if currentContent == "gear" and GearPanel.hide then
    GearPanel.hide()
  end

  if playerPanel then
    playerPanel:destroy()
    playerPanel = nil
    contentPanel = nil
    topButtonsPanel = nil
    bottomButtonsPanel = nil
    buttons = {}
  end

  currentContent = nil
end

function createButtons()
  -- Clear existing buttons and setup layouts
  setupButtonPanel(topButtonsPanel)
  setupButtonPanel(bottomButtonsPanel)
  buttons = {}

  -- Separate and sort buttons by position
  local topButtons = getButtonsByPosition("top")
  local bottomButtons = getButtonsByPosition("bottom")

  createButtonsInPanel(topButtons, topButtonsPanel)
  createButtonsInPanel(bottomButtons, bottomButtonsPanel)
end

function setupButtonPanel(panel)
  if panel then
    panel:destroyChildren()
    local layout = UIHorizontalLayout.create(panel)
    layout:setSpacing(5)
    panel:setLayout(layout)
  end
end

function getButtonsByPosition(position)
  local buttonsForPosition = {}
  for buttonId, config in pairs(buttonConfigs) do
    if config.position == position and config.enabled then
      table.insert(buttonsForPosition, {id = buttonId, config = config})
    end
  end
  table.sort(buttonsForPosition, function(a, b) return a.config.order < b.config.order end)
  return buttonsForPosition
end

function createButtonsInPanel(buttonList, panel)
  if #buttonList == 0 then
    return
  end

  for i, buttonData in ipairs(buttonList) do
    local buttonId = buttonData.id
    local config = buttonData.config

    local button = g_ui.createWidget('PlayerPanelButton', panel)
    if button then
      button:setId(buttonId)
      button:setText(config.text)

      button.onMouseRelease = function(widget, mousePos, mouseButton)
        if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
          if config.onClick then
            config.onClick()
          end
          return true
        end
        return false
      end

      buttons[buttonId] = button
    else
      g_logger.error("GamePlayerPanel: Failed to create button: " .. buttonId)
    end
  end

  -- Calculate button widths after UI is rendered
  scheduleEvent(function()
    updatePanelButtonWidths(panel)
  end, 50)
end

function updatePanelButtonWidths(panel)
  local panelButtons = panel:getChildren()
  if #panelButtons == 0 then
    return
  end

  local panelWidth = panel:getWidth()
  local spacing = 5 * (#panelButtons - 1)
  local buttonWidth = math.floor((panelWidth - spacing) / #panelButtons)

  for _, button in ipairs(panelButtons) do
    button:setWidth(buttonWidth)
    button:setFixedSize(true)
  end
end

function connectPlayerEvents()
  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    return
  end

  -- Store connections for proper cleanup
  eventConnections.ninjutsu = connect(localPlayer, {
    onNinjutsuChange = function(...)
      StatsPanel.onPlayerSkillChange('ninjutsu', ...)
    end
  })

  eventConnections.skills = connect(localPlayer, {
    onSkillChange = function(skill, level, levelPercent, oldLevel, oldLevelPercent)
      local skillMappings = {
        [0] = 'taijutsu',    -- Taijutsu
        [1] = 'kenjutsu',    -- Kenjutsu
        [2] = 'shurikenjutsu' -- Shurikenjutsu
      }

      local skillName = skillMappings[skill]
      if skillName then
        StatsPanel.onPlayerSkillChange(skillName, level, levelPercent, oldLevel, oldLevelPercent)
      end
    end
  })

  -- Connect inventory changes for gear panel
  eventConnections.inventory = connect(localPlayer, {
    onInventoryChange = function(slotId, item, oldItem)
      GearPanel.onInventoryChange(slotId, item, oldItem)
    end
  })
end

function disconnectPlayerEvents()
  for _, connection in pairs(eventConnections) do
    if connection then
      disconnect(connection)
    end
  end
  eventConnections = {}
end

function showContent(contentType)
  if currentContent == contentType then
    return -- Already showing this content
  end

  -- Hide current content panel if it has a hide function
  if currentContent == "gear" and GearPanel.hide then
    GearPanel.hide()
  end

  currentContent = contentType
  resetContentPanel()

  if contentType == "stats" then
    StatsPanel.show(contentPanel)
  elseif contentType == "gear" then
    GearPanel.show(contentPanel)
  elseif contentType == "options" then
    OptionsPanel.show(contentPanel)
  else
    showPlaceholderContent(contentType)
  end
end

function resetContentPanel()
  if not contentPanel then
    return
  end

  -- Destroy all children
  contentPanel:destroyChildren()

  -- Reset padding and margins
  contentPanel:setPaddingLeft(0)
  contentPanel:setPaddingRight(0)
  contentPanel:setPaddingTop(0)
  contentPanel:setPaddingBottom(0)

  -- Set default anchor layout
  local anchorLayout = UIAnchorLayout.create(contentPanel)
  contentPanel:setLayout(anchorLayout)
end

function showPlaceholderContent(contentType)
  local label = g_ui.createWidget('UILabel', contentPanel)
  label:setText(contentType:gsub("^%l", string.upper) .. " content\n(Not implemented yet)")
  label:setFont('verdana-11px-antialised')
  label:setColor('#888888')
  label:setTextAlign(AlignCenter)
  label:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
  label:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
end

-- Public API functions for enabling/disabling buttons
function enableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = true
    createButtons() -- Only recreate if panel exists
  end
end

function disableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = false
    createButtons() -- Only recreate if panel exists
  end
end

function addButton(buttonId, config)
  buttonConfigs[buttonId] = config
  if playerPanel then
    createButtons()
  end
end

-- Function to get the player panel for other modules
function getPlayerPanel()
  return playerPanel
end