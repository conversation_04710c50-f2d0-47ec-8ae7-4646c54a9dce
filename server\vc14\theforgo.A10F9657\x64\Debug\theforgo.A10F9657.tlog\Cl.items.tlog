C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\actions.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\actions.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\ban.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\ban.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\baseevents.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\baseevents.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\bed.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\bed.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\chat.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\chat.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\combat.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\combat.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\condition.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\condition.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\configmanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\configmanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\connection.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\connection.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\container.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\container.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\creature.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\creature.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\creatureevent.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\creatureevent.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\cylinder.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\cylinder.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\database.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\database.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\databasemanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\databasemanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\databasetasks.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\databasetasks.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\depotchest.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\depotchest.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\depotlocker.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\depotlocker.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\events.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\events.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\fileloader.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\fileloader.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\game.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\game.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\globalevent.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\globalevent.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\groups.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\groups.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\guild.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\guild.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\house.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\house.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\housetile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\housetile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\inbox.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\inbox.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\ioguild.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\ioguild.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\iologindata.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\iologindata.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\iomap.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\iomap.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\iomapserialize.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\iomapserialize.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\iomarket.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\iomarket.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\item.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\item.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\items.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\items.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\luascript.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\luascript.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\mailbox.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\mailbox.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\map.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\map.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\monster.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\monster.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\monsters.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\monsters.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\mounts.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\mounts.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\movement.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\movement.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\networkmessage.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\networkmessage.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\npc.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\npc.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\otpch.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\otpch.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\otserv.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\otserv.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\outfit.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\outfit.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\outputmessage.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\outputmessage.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\party.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\party.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\player.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\player.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\position.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\position.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\protocol.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\protocol.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\protocolgame.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\protocolgame.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\protocollogin.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\protocollogin.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\protocolold.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\protocolold.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\quests.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\quests.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\raids.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\raids.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\rsa.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\rsa.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\scheduler.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\scheduler.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\script.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\script.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\scriptmanager.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\scriptmanager.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\server.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\server.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\signals.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\signals.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\spawn.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\spawn.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\spells.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\spells.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\storeinbox.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\storeinbox.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\protocolstatus.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\protocolstatus.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\talkaction.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\talkaction.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\tasks.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\tasks.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\teleport.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\teleport.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\thing.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\thing.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\tile.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\tile.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\tools.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\tools.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\trashholder.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\trashholder.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\vocation.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\vocation.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\weapons.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\weapons.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\wildcardtree.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\wildcardtree.obj
C:\Users\<USER>\OneDrive\Desktop\projekt\server\src\xtea.cpp;C:\Users\<USER>\OneDrive\Desktop\projekt\server\vc14\theforgo.A10F9657\x64\Debug\obj_d\xtea.obj
