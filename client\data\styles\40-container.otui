PageButton < Button
  size: 30 18
  margin: 1


ContainerWindow < MiniWindow
  height: 150
  &save: true
  &containerWindow: true

  UIItem
    id: containerItemWidget
    virtual: true
    size: 16 16
    anchors.top: parent.top
    anchors.left: parent.left
    margin-top: 1
    margin-left: 3

  UIButton
    id: upButton
    anchors.top: lockButton.top
    anchors.right: lockButton.left
    margin-right: 3
    size: 14 14
    image-source: /images/ui/miniwindow_buttons
    image-clip: 42 0 14 14

    $hover:
      image-clip: 42 14 14 14

    $pressed:
      image-clip: 42 28 14 14

  Panel
    id: pagePanel
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: miniwindowTopBar.bottom
    margin: 1 3 0 3
    background: #00000066
    height: 20
    
    $on:
      visible: true

    $!on:
      visible: false

    Label
      id: pageLabel
      anchors.top: parent.top
      anchors.horizontalCenter: parent.horizontalCenter
      margin-top: 2
      text-auto-resize: true

    PageButton
      id: prevPageButton
      text: <
      anchors.top: parent.top
      anchors.left: parent.left

    PageButton
      id: nextPageButton
      text: >
      anchors.top: parent.top
      anchors.right: parent.right

  MiniWindowContents
    padding-right: 0
    layout:
      type: grid
      cell-size: 34 34
      flow: true
