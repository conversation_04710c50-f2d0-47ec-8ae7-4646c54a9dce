SmallScrollBar < UIScrollBar
  orientation: vertical
  margin-bottom: 1
  step: 20
  width: 8
  image-source: /images/ui/scrollbar
  image-clip: 39 0 13 65
  image-border: 1
  pixels-scroll: true

  UIButton
    id: decrementButton
    anchors.top: parent.top
    anchors.left: parent.left
    image-source: /images/ui/scrollbar
    image-clip: 0 0 13 13
    image-color: #ffffffff
    size: 8 8
    $hover:
      image-clip: 13 0 13 13
    $pressed:
      image-clip: 26 0 13 13
    $disabled:
      image-color: #ffffff66

  UIButton
    id: incrementButton
    anchors.bottom: parent.bottom
    anchors.right: parent.right
    size: 8 8
    image-source: /images/ui/scrollbar
    image-clip: 0 13 13 13
    image-color: #ffffffff
    $hover:
      image-clip: 13 13 13 13
    $pressed:
      image-clip: 26 13 13 13
    $disabled:
      image-color: #ffffff66

  UIButton
    id: sliderButton
    anchors.centerIn: parent
    size: 8 11
    image-source: /images/ui/scrollbar
    image-clip: 0 26 13 13
    image-border: 2
    image-color: #ffffffff
    $hover:
      image-clip: 13 26 13 13
    $pressed:
      image-clip: 26 26 13 13
    $disabled:
      image-color: #ffffff66
        
  Label
    id: valueLabel
    anchors.fill: parent
    color: white
    text-align: center