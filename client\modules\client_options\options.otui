OptionCheckBox < CheckBox
  @onCheckChange: modules.client_options.setOption(self:getId(), self:isChecked())
  height: 16

  $!first:
    margin-top: 2

OptionScrollbar < HorizontalScrollBar
  step: 1
  @onValueChange: modules.client_options.setOption(self:getId(), self:getValue())
  
OptionPanel < Panel
  layout:
    type: verticalBox
    
MainWindow
  id: optionsWindow
  !text: tr('Options')
  size: 490 500

  @onEnter: modules.client_options.hide()
  @onEscape: modules.client_options.hide()

  TabBarVertical
    id: optionsTabBar
    anchors.top: parent.top
    anchors.left: parent.left
    anchors.bottom: parent.bottom

  Panel
    id: optionsTabContent
    anchors.top: optionsTabBar.top
    anchors.left: optionsTabBar.right
    anchors.right: parent.right
    anchors.bottom: optionsTabBar.bottom
    margin-left: 10
    margin-top: 3

  Button
    !text: tr('Ok')
    width: 64
    anchors.right: parent.right
    anchors.bottom: parent.bottom
    @onClick: |
      g_settings.save()
      modules.client_options.hide()
