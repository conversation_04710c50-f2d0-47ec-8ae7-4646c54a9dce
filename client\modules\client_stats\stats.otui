DebugText < Label
  font: terminus-10px
  text-wrap: false
  text-auto-resize: true
  text-align: topleft
  anchors.right: parent.right
  anchors.left: parent.left
  anchors.top: prev.bottom
  
DebugLabel < Label
  text-wrap: false
  text-auto-resize: false
  text-align: center
  anchors.right: parent.right
  anchors.left: parent.left
  anchors.top: prev.bottom
  
MainWindow
  id: debugWindow
  size: 550 600
  !text: tr('Debug Info')
  @onClose: modules.client_stats.onClose()
  &save: false
  margin: 0 0 0 0
  padding: 25 3 3 3
  opacity: 0.9
  @onEnter: modules.client_stats.toggle()
  @onEscape: modules.client_stats.toggle()
  
  
  ScrollablePanel 
    id: debugPanel
    anchors.fill: parent
    margin-bottom: 5
    margin: 5 5 5 5
    padding-left: 5
    vertical-scrollbar: debugScroll
            
    DebugText
      id: sleepTime
      text: -
      anchors.top: parent.top

    DebugText
      id: luaRamUsage
      text: -

    DebugText
      id: atlas
      text: -

    DebugLabel
      !text: tr('Proxies')

    DebugText
      id: proxies
      text: -

    DebugLabel
      !text: tr('Main')

    DebugText
      id: mainStats
      text: -

    DebugLabel
      !text: tr('Render')

    DebugText
      id: adaptiveRender
      text: -

    DebugText
      id: render
      text: -

    DebugLabel
      !text: tr('Dispatcher')

    DebugText
      id: dispatcherStats
      text: -

    DebugLabel
      !text: tr('Lua')

    DebugText
      id: luaStats
      text: -

    DebugLabel
      !text: tr('Lua by callback')

    DebugText
      id: luaCallback
      text: -
      
    DebugLabel
      !text: tr('Widgets & Objects')

    DebugText
      id: widgetsInfo
      text: Disabled, edit stats.lua to enable     

    DebugLabel
      !text: tr('Packets')

    DebugText
      id: packets
      text: -

    DebugLabel
      !text: tr('Slow main functions')

    DebugText
      id: slowMain
      text: -

    DebugLabel
      !text: tr('Slow render functions')

    DebugText
      id: slowRender
      text: -

    DebugLabel
      !text: tr('Slow packets')

    DebugText
      id: slowPackets
      text: -
      
  VerticalScrollBar  
    id: debugScroll
    anchors.top: parent.top
    anchors.bottom: parent.bottom
    anchors.right: parent.right
    step: 48
    pixels-scroll: true
    
  ResizeBorder
    anchors.bottom: parent.bottom
    anchors.left: parent.left
    anchors.right: parent.right
  
  ResizeBorder
    anchors.right: parent.right
    anchors.top: parent.top
    anchors.bottom: parent.bottom
    
    