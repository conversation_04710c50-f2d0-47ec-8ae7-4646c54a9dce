-- ========================
-- Information Offset Configuration
-- ========================
--
-- This file configures the position adjustment of information windows 
-- for different creature outfits (looktypes).
--
-- Format options:
--   Single looktype:   looktype = {x = xOffset, y = yOffset}
--   Range of looktypes: [min_looktype, max_looktype] = {x = xOffset, y = yOffset}

--[[
  INFORMATION OFFSET GUIDE:
  
  These values control the position of information windows relative to creatures with specific looktypes.
  
  x values:
    - Negative values (-): Move the window LEFT
    - Positive values (+): Move the window RIGHT
    - Larger absolute values mean greater distance
    
  y values:
    - Negative values (-): Move the window UP
    - Positive values (+): Move the window DOWN
    - Larger absolute values mean greater distance
    
  Configuration structure:
    - Default offset (x, y): Basic offset applied when no specific condition matches
    
    - idle: Direction-specific offsets when character is STANDING STILL
      idle = {
        ["north"] = {x = xOffset, y = yOffset},  -- Applied when facing north while idle
        ["east"] = {x = xOffset, y = yOffset},   -- Applied when facing east while idle
        ["south"] = {x = xOffset, y = yOffset},  -- Applied when facing south while idle
        ["west"] = {x = xOffset, y = yOffset}    -- Applied when facing west while idle
      }
    
    - duringwalking: Direction-specific offsets when character is WALKING
      duringwalking = {
        ["north"] = {x = xOffset, y = yOffset},  -- Applied when walking north
        ["east"] = {x = xOffset, y = yOffset},   -- Applied when walking east
        ["south"] = {x = xOffset, y = yOffset},  -- Applied when walking south
        ["west"] = {x = xOffset, y = yOffset}    -- Applied when walking west
      }
]]

LooktypeOffsets = {
  -- ========================
  -- Character Outfit Offsets
  -- ========================
  
  -- Sasuke Looktypes
  -- Walk
  [2] = {
    idle = {
      ["north"] = {x = -37, y = -23},
      ["east"] = {x = -37, y = -23},
      ["south"] = {x = -37, y = -23},
      ["west"] = {x = -37, y = -23}
    }
  }, 

  -- Run
  [6] = {
    idle = {
      ["north"] = {x = -37, y = -23},
      ["east"] = {x = -37, y = -23},
      ["south"] = {x = -37, y = -23},
      ["west"] = {x = -37, y = -23}
    },
    duringwalking = {
      ["north"] = {x = -33, y = -39},
      ["east"] = {x = -4, y = -25},
      ["south"] = {x = -37, y = -23},
      ["west"] = {x = -55, y = -20}
    }
  },

  -- Kirin Startup Animation (Range of looktypes)
  [{28, 55}] = {      
    idle = {
      ["north"] = {x = -37, y = -23},
      ["east"] = {x = -37, y = -23},
      ["south"] = {x = -37, y = -23},
      ["west"] = {x = -37, y = -23}
    }
  },

  -- Chidori Run
  [219] = {
    idle = {
      ["north"] = {x = -30, y = -29},
      ["east"] = {x = -9, y = -33},
      ["south"] = {x = -45, y = 0},
      ["west"] = {x = -40, y = -16}
    },
    duringwalking = {
      ["north"] = {x = -13, y = -45},
      ["east"] = {x = 2, y = -22},
      ["south"] = {x = -36, y = 12},
      ["west"] = {x = -58, y = -3}
    }
  }
}