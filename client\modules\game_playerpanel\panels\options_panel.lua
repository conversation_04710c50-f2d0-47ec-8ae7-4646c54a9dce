-- options_panel.lua
-- Handles the options panel content and functionality

local OptionsPanel = {}

-- Local state management
local state = {
  currentOpacity = 100
}

-- Initialize opacity from settings when module loads
local function loadOpacityFromSettings()
  state.currentOpacity = g_settings.getNumber('playerPanelOpacity', 100)
end

-- Save opacity value to settings
local function saveOpacityToSettings()
  g_settings.set('playerPanelOpacity', state.currentOpacity)
  g_settings.save()
end

-- Apply opacity to the player panel
local function applyOpacityToPanel(opacity)
  local gamePlayerPanel = modules.game_playerpanel
  if gamePlayerPanel then
    local playerPanel = gamePlayerPanel.getPlayerPanel()
    if playerPanel then
      playerPanel:setOpacity(opacity / 100.0)
    end
  end
end

-- Initialize opacity from settings
loadOpacityFromSettings()

function OptionsPanel.show(contentPanel)
  if not contentPanel then
    return
  end
  
  -- Create a vertical layout for the content
  local layout = UIVerticalLayout.create(contentPanel)
  layout:setSpacing(10)
  contentPanel:setLayout(layout)
  
  OptionsPanel.createOpacityControl(contentPanel)
  OptionsPanel.createMoreOptionsButton(contentPanel)
  
  -- Apply the current opacity when showing the options panel
  applyOpacityToPanel(state.currentOpacity)
end

function OptionsPanel.clear(contentPanel)
  if contentPanel then
    contentPanel:destroyChildren()
  end
end

function OptionsPanel.createOpacityControl(contentPanel)
  -- Create container for the opacity control
  local opacityContainer = g_ui.createWidget('UIWidget', contentPanel)
  opacityContainer:setHeight(50)
  opacityContainer:setMargin(10)
  
  -- Create "Panel Opacity" label
  local opacityLabel = g_ui.createWidget('UILabel', opacityContainer)
  opacityLabel:setText('Panel Opacity')
  opacityLabel:setFont('verdana-11px-antialised')
  opacityLabel:setColor('#ffffff')
  opacityLabel:addAnchor(AnchorTop, 'parent', AnchorTop)
  opacityLabel:addAnchor(AnchorLeft, 'parent', AnchorLeft)
  opacityLabel:setMarginTop(5)
  opacityLabel:setMarginLeft(5)
  
  -- Create horizontal scrollbar as opacity slider
  local opacitySlider = g_ui.createWidget('HorizontalScrollBar', opacityContainer)
  opacitySlider:setId('opacitySlider')
  opacitySlider:addAnchor(AnchorTop, 'parent', AnchorTop)
  opacitySlider:addAnchor(AnchorLeft, 'parent', AnchorLeft)
  opacitySlider:addAnchor(AnchorRight, 'parent', AnchorRight)
  opacitySlider:setMarginTop(25)
  opacitySlider:setMarginLeft(5)
  opacitySlider:setMarginRight(5)
  opacitySlider:setHeight(20)
  opacitySlider:setMinimum(10)  -- 10% minimum opacity
  opacitySlider:setMaximum(100) -- 100% maximum opacity
  opacitySlider:setValue(state.currentOpacity)
  opacitySlider:setStep(1)
  opacitySlider.showValue = true
  opacitySlider.symbol = '%'
  
  -- Handle opacity changes
  opacitySlider.onValueChange = function(slider, value)
    OptionsPanel.onOpacityChange(value)
  end
end

function OptionsPanel.onOpacityChange(value)
  -- Store the current opacity value
  state.currentOpacity = value
  
  -- Apply opacity to the player panel
  applyOpacityToPanel(value)
  
  -- Save the new opacity value to settings
  saveOpacityToSettings()
end

function OptionsPanel.createMoreOptionsButton(contentPanel)
  -- Create container for the more options button
  local buttonContainer = g_ui.createWidget('UIWidget', contentPanel)
  buttonContainer:setHeight(50)
  buttonContainer:setMargin(10)
  
  -- Create "More Options" button
  local moreOptionsButton = g_ui.createWidget('PlayerPanelButton', buttonContainer)
  moreOptionsButton:setText('More Options')
  moreOptionsButton:setWidth(120)
  moreOptionsButton:setHeight(30)
  moreOptionsButton:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
  moreOptionsButton:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
  
  -- Handle button click
  moreOptionsButton.onMouseRelease = function(widget, mousePos, mouseButton)
    if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
      OptionsPanel.onMoreOptionsClick()
      return true
    end
    return false
  end
end

function OptionsPanel.onMoreOptionsClick()
  -- Open the main options window
  if modules.client_options then
    modules.client_options.show()
  end
end

return OptionsPanel 