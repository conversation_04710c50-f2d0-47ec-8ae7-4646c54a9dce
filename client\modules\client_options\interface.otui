OptionPanel
  Label
    width: 130
    id: layoutLabel
    !text: tr("Layout (change requries client restart)")
  
  ComboBox
    id: layout
    margin-top: 3
    margin-right: 2
    margin-left: 2
    @onOptionChange: modules.client_options.setOption(self:getId(), self:getCurrentOption().text)
    @onSetup: |
      self:addOption("Default")
      for _, file in ipairs(g_resources.listDirectoryFiles("/layouts", false, true)) do
        if g_resources.directoryExists("/layouts/" .. file) then
          self:addOption(file:gsub("^%l", string.upper))
        end
      end
    
  OptionCheckBox
    id: showPing
    !text: tr('Show connection ping')
    !tooltip: tr('Display connection speed to the server (milliseconds)')

  OptionCheckBox
    id: displayNames
    !text: tr('Display creature names')

  OptionCheckBox
    id: displayHealth
    !text: tr('Display creature health bars')

  OptionCheckBox
    id: displayHealthOnTop
    !text: tr('Display creature health bars above texts')

  OptionCheckBox
    id: hidePlayerBars
    !text: tr('Show player health bar')

  OptionCheckBox
    id: displayMana
    !text: tr('Show player mana bar')

  OptionCheckBox
    id: topHealtManaBar
    !text: tr('Show player top health and mana bar')

  OptionCheckBox
    id: showHealthManaCircle
    !text: tr('Show health and mana circle')

  OptionCheckBox
    id: highlightThingsUnderCursor
    !text: tr('Highlight things under cursor')

  Label
    margin-top: 3
    id: crosshairLabel
    !text: tr("Crosshair")
    
  ComboBox
    id: crosshair
    margin-top: 3
    margin-right: 2
    margin-left: 2
    @onOptionChange: modules.client_options.setOption(self:getId(), self.currentIndex)
    @onSetup: |
      self:addOption("None")
      self:addOption("Default")
      self:addOption("Full")

  Label
    id: floorFadingLabel
    margin-top: 6
    @onSetup: |
      local value = modules.client_options.getOption('floorFading')
      self:setText(tr('Floor fading: %s ms', value))

  OptionScrollbar
    id: floorFading
    margin-top: 3
    minimum: 0
    maximum: 2000

  Label
    id: floorFadingLabel2
    margin-top: 6
    !text: (tr('Floor fading doesn\'t work with enabled light'))
