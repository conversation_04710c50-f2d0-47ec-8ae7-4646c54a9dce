--[[
  Game Conditions Module
  Handles player status conditions (poison, burn, etc.)
]]

-- Constants
local CONDITION_OPCODE = 69

-- Condition Types (for reference)
local CONDITION_TYPES = {
  POISON = 1,
  BURN = 2,
  ENERGY = 4,
  HASTE = 16,
  PARALYZE = 32
}

-- Mapping of condition types to icon filenames
local conditionIcons = {
  [CONDITION_TYPES.POISON] = "poison.png",
  [CONDITION_TYPES.BURN] = "burn.png",
  [CONDITION_TYPES.ENERGY] = "energy.png",
  [CONDITION_TYPES.HASTE] = "haste.png",
  [CONDITION_TYPES.PARALYZE] = "paralyze.png"
  -- Add more mappings as needed
}

-- Module variables
conditionContainer = nil -- Needs to be global for module access
local activeConditions = {} -- Table to track active conditions

--[[ Module Lifecycle Functions ]]--

function init()
  print("Initializing game_conditions module...")

  -- Connect to game events
  connect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  -- Register extended JSON opcode handler
  ProtocolGame.registerExtendedJSONOpcode(CONDITION_OPCODE, onExtendedJSONOpcode)

  -- Initialize if player is already logged in
  if g_game.isOnline() then
    online()
  end
end

function terminate()
  print("Terminating game_conditions module.")

  -- Clean up if player is online
  if g_game.isOnline() then
    offline()
  end

  -- Disconnect from game events
  disconnect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  -- Unregister extended JSON opcode handler
  ProtocolGame.unregisterExtendedJSONOpcode(CONDITION_OPCODE)
end

function online()
  -- Create UI container
  local rootGamePanel = modules.game_interface.getRootPanel()
  conditionContainer = g_ui.loadUI('game_conditions', rootGamePanel)

  -- Reset active conditions
  activeConditions = {}
end

function offline()
  -- Clean up all active conditions
  for _, widget in pairs(activeConditions) do
    removeCondition(widget)
  end

  -- Destroy the container
  if conditionContainer then
    conditionContainer:destroy()
    conditionContainer = nil
  end

  -- Clear active conditions table
  activeConditions = {}
end

--[[ Protocol Handling Functions ]]--

function onExtendedJSONOpcode(protocol, opcode, data)
  -- Ignore if not our opcode
  if opcode ~= CONDITION_OPCODE then return end

  -- Debug log
  print("Received condition data:", json.encode(data))

  -- Validate data
  if not data or type(data) ~= "table" then
    print("Error: Invalid condition data received (nil or not a table)")
    return
  end

  if not data.action then
    print("Error: Missing 'action' field in condition data")
    return
  end

  -- Route to appropriate handler
  if data.action == "add" then
    handleAddCondition(data)
  elseif data.action == "remove" then
    handleRemoveCondition(data)
  else
    print("Warning: Unknown condition action:", data.action)
  end
end

-- Generate a unique key for a condition
local function getConditionKey(data)
  if not data.type then return nil end
  return data.type .. "-" .. (data.subId or 0)
end

function handleAddCondition(data)
  -- Validate required fields
  if not data.type then
    print("Error: Condition type missing in add action")
    return
  end

  -- Generate a unique key for this condition
  local conditionKey = getConditionKey(data)
  if not conditionKey then return end

  -- Check if we have an icon for this condition type
  local iconFile = conditionIcons[data.type]
  if not iconFile then
    print("Warning: No icon defined for condition type:", data.type, ". Attempting to use placeholder.")
    iconFile = "generic_condition_placeholder_trigger.png" -- This dummy name will trigger placeholder logic in addCondition
  end

  -- Calculate duration in seconds from ticks (if provided)
  local duration = -1
  if data.ticks and data.ticks > 0 then
    duration = math.ceil(data.ticks / 1000) -- Convert milliseconds to seconds
  end

  -- Remove existing condition if it exists
  if activeConditions[conditionKey] then
    removeCondition(activeConditions[conditionKey])
    activeConditions[conditionKey] = nil
  end

  -- Add the new condition icon
  local iconWidget = addCondition(iconFile, duration)
  if iconWidget then
    activeConditions[conditionKey] = iconWidget
    -- Store condition data in the widget for reference
    iconWidget.conditionData = data
  end
end

function handleRemoveCondition(data)
  -- Validate required fields
  if not data.type then
    print("Error: Condition type missing in remove action")
    return
  end

  -- Generate the condition key
  local conditionKey = getConditionKey(data)
  if not conditionKey then return end

  -- Remove the condition if it exists
  if activeConditions[conditionKey] then
    removeCondition(activeConditions[conditionKey])
    activeConditions[conditionKey] = nil
  end
end

--[[ Utility Functions ]]--

-- Format time as MM:SS
local function formatTime(seconds)
  if not seconds or seconds < 0 then
    return "--"
  end

  local minutes = math.floor(seconds / 60)
  local remainingSeconds = seconds % 60
  return string.format("%d:%02d", minutes, remainingSeconds)
end

-- Find condition key by widget reference
local function findConditionKeyByWidget(widget)
  for key, condWidget in pairs(activeConditions) do
    if condWidget == widget then
      return key
    end
  end
  return nil
end

--[[ UI Management Functions ]]--

-- Remove a condition icon and clean up resources
function removeCondition(iconWidget)
  if not iconWidget or iconWidget:isDestroyed() then
    return
  end

  -- Cancel the update event if it exists
  if iconWidget.updateEvent then
    removeEvent(iconWidget.updateEvent)
    iconWidget.updateEvent = nil
  end

  -- Destroy the widget
  iconWidget:destroy()
end

-- Create and start a timer for a condition
local function startConditionTimer(iconWidget, timerLabel, duration)
  -- For infinite duration, just set the label and return
  if duration == -1 then
    timerLabel:setText("--")
    return
  end

  -- Set up timer for the condition duration
  local remainingTime = duration

  -- Update the timer label initially
  timerLabel:setText(formatTime(remainingTime))

  -- Create a cyclic event to update the timer every second
  local updateEvent = cycleEvent(function()
    remainingTime = remainingTime - 1

    -- Update the timer display
    if remainingTime > 0 then
      timerLabel:setText(formatTime(remainingTime))
    else
      -- Time's up, remove the condition icon
      local conditionKey = findConditionKeyByWidget(iconWidget)
      if conditionKey then
        activeConditions[conditionKey] = nil
      end

      removeCondition(iconWidget)
      return true -- Cancel the cyclic event
    end

    return false -- Continue the event
  end, 1000) -- Update every 1000ms (1 second)

  -- Store the event ID in the widget for cleanup
  iconWidget.updateEvent = updateEvent
end

-- Add a condition icon with a duration
function addCondition(iconId, duration)
  -- Validate container
  if not conditionContainer then
    print("Error: conditionContainer is not initialized")
    return nil
  end

  -- Validate icon path
  local iconPath = "/data/images/game/conditions/" .. iconId
  if not g_resources.fileExists(iconPath) then
    print("Warning: Icon file not found at " .. iconPath .. ". Using placeholder.")
    iconPath = "/data/images/game/conditions/placeholder.png" -- Use placeholder
    -- Optionally, check if placeholder exists and handle that too, or use a very generic UI icon
    if not g_resources.fileExists(iconPath) then
      print("Error: Placeholder icon not found at " .. iconPath .. ". Cannot display condition icon.")
      -- As a last resort, you could try a known existing UI element or simply not create the widget
      -- For now, let's assume placeholder.png should exist or this will fail.
      -- Alternatively, one could use a very generic icon from another path if available
      -- e.g., iconPath = "/data/images/ui/unknown_icon.png" if such a generic icon exists
      return nil -- Or handle differently, e.g., create widget without an image
    end
  end

  -- Create the icon widget
  local iconWidget = g_ui.createWidget('IconTemplate', conditionContainer)
  local timerLabel = iconWidget:getFirstChild()
  iconWidget:setImageSource(iconPath)

  -- Start the timer
  startConditionTimer(iconWidget, timerLabel, duration)

  return iconWidget
end