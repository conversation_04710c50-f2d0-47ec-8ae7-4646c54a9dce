EnterGameWindow
  id: enterGame
  @onEnter: EnterGame.doLogin()

  MenuLabel
    !text: tr('Account name')
    anchors.left: parent.left
    anchors.top: parent.top
    text-auto-resize: true

  TextEdit
    id: accountNameTextEdit
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 2

  MenuLabel
    !text: tr('Password')
    anchors.left: prev.left
    anchors.top: prev.bottom
    margin-top: 8
    text-auto-resize: true

  PasswordTextEdit
    id: accountPasswordTextEdit
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 2
    
  MenuLabel
    !text: tr('Token')
    anchors.left: prev.left
    anchors.top: prev.bottom
    text-auto-resize: true
    margin-top: 8

  TextEdit
    id: accountTokenTextEdit
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 2

  Panel
    id: serverSelectorPanel
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    height: 52
    on: true
    focusable: false
    
    $on:
      visible: true
      margin-top: 0
    
    $!on:
      visible: false
      margin-top: -52
      
    HorizontalSeparator
      anchors.left: parent.left
      anchors.right: parent.right
      anchors.top: parent.top
      margin-top: 10

    MenuLabel
      id: serverLabel
      !text: tr('Server')
      anchors.left: parent.left
      anchors.top: prev.bottom
      text-auto-resize: true    
      margin-top: 5

    ComboBox
      id: serverSelector
      anchors.left: prev.left
      anchors.right: parent.right
      anchors.top: serverLabel.bottom
      margin-top: 2
      margin-right: 3
      menu-scroll: true
      menu-height: 125
      menu-scroll-step: 25
      text-offset: 5 2
      @onOptionChange: EnterGame.onServerChange()
    
  Panel
    id: customServerSelectorPanel
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    height: 52
    on: true
    focusable: true
    
    $on:
      visible: true
      margin-top: 0
    
    $!on:
      visible: false
      margin-top: -52
    
    HorizontalSeparator
      anchors.left: parent.left
      anchors.right: parent.right
      anchors.top: parent.top
      margin-top: 8

    MenuLabel
      id: serverLabel
      !text: tr('IP:PORT or URL')
      anchors.left: prev.left
      anchors.top: prev.bottom
      margin-top: 8
      text-auto-resize: true

    TextEdit
      id: serverHostTextEdit
      !tooltip: tr('Make sure that your client uses\nthe correct game client version')
      anchors.left: parent.left
      anchors.top: serverLabel.bottom
      margin-top: 2
      width: 150

    MenuLabel
      id: clientLabel
      !text: tr('Version')
      anchors.left: serverHostTextEdit.right
      anchors.top: serverLabel.top
      text-auto-resize: true
      margin-left: 10

    ComboBox
      id: clientVersionSelector
      anchors.top: serverHostTextEdit.top
      anchors.bottom: serverHostTextEdit.bottom
      anchors.left: prev.left
      anchors.right: parent.right
      menu-scroll: true
      menu-height: 125
      menu-scroll-step: 25
      margin-right: 3
      
  HorizontalSeparator
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 10

  CheckBox
    id: rememberPasswordBox
    !text: tr('Remember password')
    !tooltip: tr('Remember account and password when starts client')
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 9

  HorizontalSeparator
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 9

  Button
    !text: tr('Login')
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.top: prev.bottom
    margin-top: 10
    margin-left: 50
    margin-right: 50
    @onClick: EnterGame.doLogin()

  Label
    id: serverInfoLabel
    font: verdana-11px-rounded
    anchors.top: prev.top
    anchors.left: parent.left
    margin-top: 5
    color: green
    text-auto-resize: true