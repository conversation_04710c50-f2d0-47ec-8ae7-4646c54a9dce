But<PERSON> < UIButton
  font: verdana-11px-antialised
  color: #dfdfdfff
  size: 106 23
  text-offset: 0 1
  image-source: /images/ui/button
  image-color: #dfdfdf
  image-clip: 0 0 22 23
  image-border: 3
  padding: 5 10 5 10
  opacity: 1.0

  $hover !disabled:
    image-clip: 0 23 22 23

  $pressed:
    image-clip: 0 46 22 23
    text-offset: 1 1

  $disabled:
    color: #dfdfdf88
    opacity: 0.8

TabButton < UIButton
  size: 22 23
  image-source: /images/ui/tabbutton_rounded
  image-color: #dfdfdf
  image-clip: 0 0 22 23
  image-border: 3
  text-offset: 0 1
  icon-color: #dfdfdf
  color: #dfdfdf

  $hover !on:
    image-clip: 0 23 22 23
    color: #dfdfdf

  $disabled:
    image-color: #dfdfdf66
    icon-color: #dfdfdf

  $on:
    image-clip: 0 46 22 23
    color: #dfdfdf

NextButton < UIButton
  size: 12 21
  image-source: /images/ui/arrow_horizontal
  image-clip: 12 0 12 21
  image-color: #ffffff
  text-offset: 0 1

  $hover !disabled:
    image-clip: 12 21 12 21

  $pressed:
    image-clip: 12 21 12 21

  $disabled:
    image-color: #dfdfdf88

PreviousButton < UIButton
  size: 12 21
  image-source: /images/ui/arrow_horizontal
  image-clip: 0 0 12 21
  image-color: #ffffff
  text-offset: 0 1

  $hover !disabled:
    image-clip: 0 21 12 21

  $pressed:
    image-clip: 0 21 12 21

  $disabled:
    image-color: #dfdfdf88

AddButton < UIButton
  size: 20 20
  image-source: /images/ui/icon_add
  image-color: #dfdfdfff
  text-offset: 0 1

  $hover !disabled:
    image-color: #dfdfdf99

  $pressed:
    image-color: #dfdfdf44

  $disabled:
    image-color: #dfdfdf55
