---
name: Merge otcv8-dev
'on':
  - workflow_dispatch

jobs:
  Windows:
    name: Merge otcv8-dev
    runs-on: windows-latest
    timeout-minutes: 60
    steps:
      - name: Checkout otclientv8
        uses: actions/checkout@v2
        with:
          submodules: recursive
          path: otclient

      - name: Checkout otcv8-dev
        uses: actions/checkout@v2
        with:
          repository: OTCv8/otcv8-dev
          path: otcv8-dev
          submodules: recursive
          token: "${{ secrets.otcv8_token }}"

      - name: Update files
        run: |
          cd otclient
          git rm `
            -f -r --ignore-unmatch `
          src/client data modules layouts mods tests init.lua test.lua
          git checkout HEAD -- src/client/CMakeLists.txt
          cd ..
          robocopy otcv8-dev/src/client otclient/src/client /s
          robocopy otcv8-dev/data otclient/data /s
          robocopy otcv8-dev/modules otclient/modules /s
          robocopy otcv8-dev/layouts otclient/layouts /s
          robocopy otcv8-dev/mods otclient/mods /s
          robocopy otcv8-dev/tests otclient/tests /s
          robocopy otcv8-dev otclient init.lua test.lua tests.7z
          exit 0 # required, robocopy has exit code 1

      - name: Setup MSBuild and add to PATH
        uses: microsoft/setup-msbuild@v1.0.2

      - name: Run vcpkg
        uses: lukka/run-vcpkg@v7.1
        with:
          vcpkgDirectory: ${{ runner.workspace }}/vcpkg/
          vcpkgTriplet: x86-windows-static
          vcpkgGitCommitId: 9a49e3df7f729655318c59b9985c9c18ad0c99d6
          vcpkgArguments: >
            boost-iostreams boost-asio boost-beast boost-system boost-variant boost-lockfree boost-process boost-program-options boost-uuid boost-filesystem
            luajit glew physfs openal-soft libogg libvorbis zlib libzip bzip2 openssl

      - name: Integrate vcpkg
        run: |
          ${{ runner.workspace }}\vcpkg\vcpkg integrate install

      - name: Compile otclient_dx
        timeout-minutes: 20
        run: |
          cd otclient/vc16
          MSBuild /property:Configuration=DirectX

      - name: Run tests
        timeout-minutes: 10
        run: |
          cd otclient
          7z x tests.7z -aoa
          $process = (Start-Process -Wait -PassThru otclient_dx.exe --test)
          Get-Content otclientv8.log
          if ($process.ExitCode -ne 0) {
            throw "Invalid exit code: " + $process.ExitCode;
          }

      - name: Commit changes
        uses: EndBug/add-and-commit@v7
        with:
          cwd: otclient
          push: true
          message: "Merged with otcv8-dev (${{ github.run_number }})"
