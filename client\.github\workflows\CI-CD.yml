name: Build, Test, Release
on:
  push:
    branches:
      - main
      - master
  workflow_run:
    workflows:
      - "Merge otcv8-dev"
    types:
      - completed

jobs:
  Windows:
    name: Build windows version
    runs-on: windows-latest
    timeout-minutes: 90
    if: |
      !contains(github.event.head_commit.message, '[skip release]')
      && (
        startsWith(github.ref, 'refs/tags/')
        || github.ref == 'refs/heads/main'
        || github.ref == 'refs/heads/master'
      )
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
          submodules: recursive

    - name: Setup MSBuild and add to PATH
      uses: microsoft/setup-msbuild@v1.0.2
      id: setup_msbuild

    - name: Run vcpkg
      uses: lukka/run-vcpkg@v7.1
      with:
        vcpkgDirectory: ${{ runner.workspace }}/vcpkg/
        vcpkgTriplet: x86-windows-static
        vcpkgGitCommitId: 9a49e3df7f729655318c59b9985c9c18ad0c99d6
        vcpkgArguments: >
          boost-iostreams boost-asio boost-beast boost-system boost-variant boost-lockfree boost-process boost-program-options boost-uuid boost-filesystem
          luajit glew physfs openal-soft libogg libvorbis zlib libzip bzip2 openssl

    - name: Integrate vcpkg
      run: |
        ${{ runner.workspace }}\vcpkg\vcpkg integrate install
        
    - name: Compile 
      timeout-minutes: 60
      run: |
        cd vc16
        MSBuild /property:Configuration=DirectX /p:BUILD_REVISION=${{github.run_number}}
        MSBuild /property:Configuration=OpenGL /p:BUILD_REVISION=${{github.run_number}}
        MSBuild /property:Configuration=Debug_lib /p:BUILD_REVISION=${{github.run_number}}
        MSBuild /property:Configuration=Debug /p:BUILD_REVISION=${{github.run_number}}

    - name: Compress debug lib
      run: |
        7z a `
          -t7z `
        lib.7z `
        otclient_debug_lib.lib otclient_debug_lib.pdb      

    - name: Upload binaries
      uses: 'actions/upload-artifact@v2'
      with:
        name: Binaries
        path: |
          otclient_gl.exe
          otclient_dx.exe
          otclient_debug.exe
          lib.7z
        if-no-files-found: error

  Mac:
    name: Build mac os version
    runs-on: macos-latest
    timeout-minutes: 60
    if: |
      !contains(github.event.head_commit.message, '[skip release]')
      && (
        startsWith(github.ref, 'refs/tags/')
        || github.ref == 'refs/heads/main'
        || github.ref == 'refs/heads/master'
      )
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
          submodules: recursive

    - name: Get latest CMake
      uses: lukka/get-cmake@latest

    - name: MacOS - install physfs pkgconfig luajit xquartz
      run: brew install physfs pkgconfig luajit xquartz

    - name: Run vcpkg
      uses: lukka/run-vcpkg@v7.1
      with:
        vcpkgArguments: >
          boost-iostreams boost-asio boost-system boost-variant boost-lockfree boost-beast glew 
          boost-filesystem boost-uuid libogg libvorbis zlib opengl libzip openal-soft bzip2
          boost-process openssl
        vcpkgDirectory: ${{ runner.workspace }}/vcpkg/
        vcpkgTriplet: x64-osx
        vcpkgGitCommitId: 9a49e3df7f729655318c59b9985c9c18ad0c99d6

    - name: Build with CMake
      uses: lukka/run-cmake@v3
      with:
        buildDirectory: ${{ runner.workspace }}/build
        cmakeAppendedArgs: '-G Ninja -DCMAKE_BUILD_TYPE="Release" -DVERSION=${{github.run_number}}'
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        useVcpkgToolchainFile: true

    - name: Change name
      run: |
        mv '${{ runner.workspace }}/build/otclient' '${{ runner.workspace }}/build/otclient_mac'

    - name: Upload otclient
      uses: actions/upload-artifact@v2
      with:
        name: Binaries
        path: |
          ${{ runner.workspace }}/build/otclient_mac
        if-no-files-found: error

  Linux:
    name: Build linux version
    runs-on: ubuntu-20.04
    timeout-minutes: 60
    if: |
      !contains(github.event.head_commit.message, '[skip release]')
      && (
        startsWith(github.ref, 'refs/tags/')
        || github.ref == 'refs/heads/main'
        || github.ref == 'refs/heads/master'
      )
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
          submodules: recursive

    - name: Get latest CMake
      uses: lukka/get-cmake@latest

    - name: Ubuntu - install opengl lua5.1 luajit
      run: sudo apt-get install libglew-dev liblua5.1-0-dev libluajit-5.1-dev

    - name: Run vcpkg
      uses: lukka/run-vcpkg@v7.1
      with:
        vcpkgArguments: >
          boost-iostreams boost-asio boost-system boost-variant boost-lockfree boost-beast glew 
          boost-filesystem boost-uuid libogg libvorbis zlib opengl libzip openal-soft bzip2
          boost-process openssl physfs
        vcpkgDirectory: ${{ runner.workspace }}/vcpkg/
        vcpkgTriplet: x64-linux
        vcpkgGitCommitId: 9a49e3df7f729655318c59b9985c9c18ad0c99d6

    - name: Build with CMake
      uses: lukka/run-cmake@v3
      with:
        buildDirectory: ${{ runner.workspace }}/build
        cmakeAppendedArgs: '-G Ninja -DCMAKE_BUILD_TYPE="Release" -DVERSION=${{github.run_number}}'
        cmakeListsOrSettingsJson: CMakeListsTxtAdvanced
        useVcpkgToolchainFile: true

    - name: Change name
      run: |
        mv '${{ runner.workspace }}/build/otclient' '${{ runner.workspace }}/build/otclient_linux'

    - name: Upload otclient
      uses: actions/upload-artifact@v2
      with:
        name: Binaries
        path: |
          ${{ runner.workspace }}/build/otclient_linux
        if-no-files-found: error

  Release:
    name: Test and release
    runs-on: windows-latest
    needs: [Windows, Mac, Linux]
    timeout-minutes: 120
    if: |
      !contains(github.event.head_commit.message, '[skip release]')
      && (
        startsWith(github.ref, 'refs/tags/')
        || github.ref == 'refs/heads/main'
        || github.ref == 'refs/heads/master'
      )
    steps:
    - name: Checkout
      uses: actions/checkout@v2
      with:
        submodules: recursive

    - name: Download binaries
      uses: actions/download-artifact@v2
      with:
        name: Binaries

    - name: Run tests
      timeout-minutes: 10
      run: |
        7z x tests.7z -aoa
        $process = (Start-Process -Wait -PassThru otclient_debug.exe --test)
        Get-Content otclientv8.log
        if ($process.ExitCode -ne 0) {
          throw "Invalid exit code: " + $process.ExitCode;
        }

    - name: Upload otclientv8.log
      uses: actions/upload-artifact@v2
      with:
        path: otclientv8.log
        name: Download-otclientv8.log
        if-no-files-found: error

    - name: Copy screenshots
      run: |
        $location = Get-Location
        $pillowScript = @"
        from PIL import Image
        import glob

        for file in glob.glob('*.png'):
          Image.open(file).convert('RGB').save(
            file[:-3] + 'jpg',
            quality=50,
          )
        "@
        cd $env:appdata\otclientv8\otclientv8
        pip3 install pillow
        python3 -c $pillowScript
        cd $location
        mkdir screenshots
        Get-ChildItem $env:appdata\otclientv8\otclientv8 -Filter *.jpg `
        | Copy-Item -Destination screenshots -Force -PassThru

    - name: Upload screenshot
      uses: actions/upload-artifact@v2
      with:
        path: screenshots
        name: Download-screenshots
    
    - name: Cleanup things
      run: |
        Remove-Item 'data\things' -Recurse

    - name: Cleanup src
      run: |
        Remove-Item 'src\android' -Recurse
        Get-ChildItem -Path 'src\framework' *.c* -Recurse `
        | foreach { Remove-Item -Path $_.FullName }

    - name: Set version
      run: |
        $version = Select-String -path otclientv8.log '(OTCv8[^(]+)' |
          select-object -First 1 |
          % { $_.Matches } |
          % { $_.Value }
        "VERSION=${version}" | Add-Content -Path $ENV:GITHUB_ENV

    - name: Upload otclient
      uses: actions/upload-artifact@v2
      with:
        name: Download-otclientv8
        path: |
          otclient_gl.exe
          otclient_dx.exe
          otclient_mac
          otclient_linux
          *.dll
          data
          modules
          layouts
          mods
          init.lua
        if-no-files-found: error

    - name: Checkout public otclientv8
      uses: actions/checkout@v2
      with:
        repository: OTCv8/otclientv8
        path: otclientv8
        submodules: recursive
        token: "${{ secrets.otcv8_token }}"

    - name: Update public otclientv8 files
      run: |
        cd otclientv8
        git rm . -f -r --ignore-unmatch
        git checkout HEAD -- README.md LICENSE
        cd ..

        robocopy `
        . `
        otclientv8 `
        init.lua `
        otclient_dx.exe `
        otclient_gl.exe `
        otclient_mac `
        otclient_linux `
        libGLESv2.dll `
        libEGL.dll `
        d3dcompiler_47.dll

        robocopy data otclientv8/data /s
        robocopy layouts otclientv8/layouts /s
        robocopy mods otclientv8/mods /s
        robocopy modules otclientv8/modules /s
        exit 0 # required, robocopy has exit code 1

    - name: Commit public otclientv8
      uses: EndBug/add-and-commit@v7
      with:
        cwd: otclientv8
        push: true
        author_name: OTCv8
        author_email: <EMAIL>
        message: "Updated to ${{ env.VERSION }}"

    - name: Checkout otcv8-dev
      uses: actions/checkout@v2
      with:
        repository: OTCv8/otcv8-dev
        path: otcv8-dev
        submodules: recursive
        token: "${{ secrets.otcv8_token }}"

    - name: Update otcv8-dev files
      run: |
        cd otcv8-dev
        git rm `
          -f -r --ignore-unmatch `
        src data modules layouts mods tests init.lua test.lua lib.7z tests.7z
        cd ..
        robocopy src otcv8-dev/src
        robocopy src/client otcv8-dev/src/client *.h* *.c* /s
        robocopy src/framework otcv8-dev/src/framework *.h* /s
        robocopy data otcv8-dev/data /s
        robocopy modules otcv8-dev/modules /s
        robocopy layouts otcv8-dev/layouts /s
        robocopy mods otcv8-dev/mods /s
        robocopy tests otcv8-dev/tests /s
        robocopy . otcv8-dev init.lua test.lua tests.7z lib.7z

        exit 0 # required, robocopy has exit code 1

    - name: Commit otcv8-dev
      uses: EndBug/add-and-commit@v7
      with:
        cwd: otcv8-dev
        push: true
        author_name: OTCv8
        author_email: <EMAIL>
        message: "Updated to ${{ env.VERSION }}"

    - name: Deploy on otclient.ovh
      uses: garygrossgarten/github-action-ssh@release
      with:
        command: ./update_otcv8.sh
        host: otclient.ovh
        username: debian
        privateKey: "${{ secrets.SSH_KEY }}"
