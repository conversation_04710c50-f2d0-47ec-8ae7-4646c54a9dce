CheckBox < UICheckBox
  size: 16 16
  text-align: left
  text-offset: 18 1
  color: #dfdfdf
  image-color: #dfdfdfff
  image-rect: 0 0 15 15
  image-source: /images/ui/checkbox

  $hover !disabled:
    color: #ffffff

  $!checked:
    image-clip: 0 0 15 15

  $hover !checked:
    image-clip: 0 15 15 15

  $checked:
    image-clip: 0 30 15 15

  $hover checked:
    image-clip: 0 45 15 15

  $disabled:
    image-color: #dfdfdf88
    color: #dfdfdf88
    opacity: 0.8

ColorBox < UICheckBox
  size: 16 16
  image-color: #dfdfdfff
  image-source: /images/ui/colorbox

  $checked:
    image-clip: 16 0 16 16

  $!checked:
    image-clip: 0 0 16 16

ButtonBox < UICheckBox
  font: verdana-11px-antialised
  color: #dfdfdfff
  size: 106 23
  text-offset: 0 0
  text-align: center
  image-source: /images/ui/button
  image-color: #dfdfdf
  image-clip: 0 0 22 23
  image-border: 3

  $hover !disabled:
    image-clip: 0 23 22 23

  $checked:
    image-clip: 0 46 22 23
    color: #dfdfdf

  $disabled:
    color: #dfdfdf88
    image-color: #dfdfdf88

ButtonBoxRounded < ButtonBox
  image-source: /images/ui/button_rounded